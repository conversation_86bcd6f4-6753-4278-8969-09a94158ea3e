import { Component, Input, OnInit, On<PERSON><PERSON>roy, ChangeDetectionStrategy, signal, inject, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatToolbarModule } from '@angular/material/toolbar';

import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

import { DragDropModule } from '@angular/cdk/drag-drop';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { DynamicLayoutRendererModalService } from '../../modals/dynamic-layout-renderer-modal/dynamic-layout-renderer-modal.service';

// Import FlashMessageService
import { FlashMessageService } from '@core/services/flash_message.service';


// Import interface mới từ dynamic-layout-config.model.ts
import { DynamicLayoutConfig, DynamicLayoutBuilderConfig } from '@/shared/components/dynamic-layout-builder/models/dynamic-layout-config.model';

// Import các interface khác từ model cũ
import { TabComponentReference } from '@/shared/components/dynamic-layout-builder/models/dynamic-layout-builder.model';
import { MultiLayoutManagementService } from '../../services/multi-layout-management.service';
import { TabStateManagementService } from '../../services/tab-state-management.service';
import { DynamicLayoutBuilderStateService } from '../../services/dynamic-layout-builder-state.service';
import { CreateLayoutModalService } from '../../modals/create-layout-modal/create-layout-modal.service';
import { LayoutSelectorService } from './layout-selector/layout-selector.service';
import { DynamicLayoutConfigStateService, LayoutSwitchConfirmData } from '../../services/dynamic-layout-config-state.service';
import { UnsavedChangesConfirmModalService } from '../../modals/unsaved-changes-confirm-modal/unsaved-changes-confirm-modal.service';

import { CreateTabComponent } from './tabs/create-tab/create-tab.component';
import { QuickCreateTabComponent } from './tabs/quick-create-tab/quick-create-tab.component';
import { DetailViewTabComponent } from './tabs/detail-view-tab/detail-view-tab.component';
import { LayoutSelectorComponent } from './layout-selector/layout-selector.component';
import { getElementMaxHeightToFit100vh } from '@shared/utils';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatTabsModule } from '@angular/material/tabs';
import { MatMenuModule } from '@angular/material/menu';



/**
 * DynamicLayoutBuilderComponent - Component chính để tạo và quản lý layout khách hàng
 *
 * Tính năng chính:
 * - Kéo-thả để tạo sections và fields
 * - Chỉnh sửa inline tên section và field labels
 * - Quản lý thuộc tính và quyền của fields
 * - Template ngành hàng có sẵn
 * - Lưu/khôi phục layout
 * - Preview dữ liệu khách hàng
 *
 * IMPORTANT: Component này provide DynamicLayoutBuilderService ở component level
 * để đảm bảo mỗi instance có state riêng biệt, tránh conflicts khi có multiple instances.
 */
@Component({
  selector: 'app-dynamic-layout-builder',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatToolbarModule,
    MatProgressSpinnerModule,
    DragDropModule,
    TranslateModule,
    CreateTabComponent,
    QuickCreateTabComponent,
    DetailViewTabComponent,
    LayoutSelectorComponent,
    MatTooltipModule,
    MatTabsModule,
    MatMenuModule
  ],
  providers: [
    LayoutSelectorService,
    CreateLayoutModalService,
    {
      provide: DynamicLayoutConfigStateService,
      useFactory: (parent: DynamicLayoutBuilderComponent) => parent.getDynamicLayoutConfigStateService(),
      deps: [DynamicLayoutBuilderComponent]
    },

    {
      provide: DynamicLayoutBuilderStateService,
      useFactory: (parent: DynamicLayoutBuilderComponent) => parent.getDynamicLayoutBuilderStateService(),
      deps: [DynamicLayoutBuilderComponent]
    },
    {
      provide: MultiLayoutManagementService,
      useFactory: (parent: DynamicLayoutBuilderComponent) => parent.getMultiLayoutManagementService(),
      deps: [DynamicLayoutBuilderComponent]
    },
    {
      provide: TabStateManagementService,
      useFactory: (parent: DynamicLayoutBuilderComponent) => parent.getTabStateManagementService(),
      deps: [DynamicLayoutBuilderComponent]
    }
  ],
  templateUrl: './dynamic-layout-builder.component.html',
  styleUrls: ['./dynamic-layout-builder.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DynamicLayoutBuilderComponent implements OnInit, OnDestroy, AfterViewInit {
  @Input({ required: true }) layoutBuilderConfig: DynamicLayoutBuilderConfig = {
    layoutId: '',
    layouts: [],
    defaultLayoutConfig: {} as DynamicLayoutConfig
  };

  @ViewChild('content', { static: true }) content!: ElementRef<HTMLElement>;
  @ViewChild('createTab') createTab!: TabComponentReference;
  @ViewChild('quickCreateTab') quickCreateTab!: TabComponentReference;
  @ViewChild('detailViewTab') detailViewTab!: TabComponentReference;

  // ✅ REFACTORED: Non-injectable services - instantiate với 'new'
  private multiLayoutManagementService = new MultiLayoutManagementService();
  private tabStateManagementService = new TabStateManagementService();

  // ✅ REFACTORED: DynamicLayoutBuilderStateService cần dependencies
  private translateService = inject(TranslateService);
  private flashMessageService = inject(FlashMessageService);
  private dynamicLayoutBuilderStateService = new DynamicLayoutBuilderStateService(
    this.flashMessageService,
    this.translateService
  );

  private dynamicLayoutConfigStateService = new DynamicLayoutConfigStateService();
  private unsavedChangesConfirmModalService = inject(UnsavedChangesConfirmModalService);
  private dynamicLayoutRendererModalService = inject(DynamicLayoutRendererModalService);
  private subscriptions = new Subscription();


  
  // Signals để quản lý trạng thái
  isPreviewMode = signal<boolean>(false);

  // Signal để quản lý layout hiện tại
  selectedTabIndex = signal<number>(1); // Default to Quick Create tab (index 1)
  hasQuickCreate = signal(true);
  hasDetailViewConfig = signal(true);

  // ==================== PUBLIC METHODS FOR CHILD COMPONENTS ====================

  /**
   * ✅ NEW: Getter method để child components có thể access service instance
   */
  public getDynamicLayoutConfigStateService(): DynamicLayoutConfigStateService {
    return this.dynamicLayoutConfigStateService;
  }



  /**
   * ✅ NEW: Getter method để child components có thể access DynamicLayoutBuilderStateService instance
   */
  public getDynamicLayoutBuilderStateService(): DynamicLayoutBuilderStateService {
    return this.dynamicLayoutBuilderStateService;
  }

  /**
   * ✅ NEW: Getter method để child components có thể access MultiLayoutManagementService instance
   */
  public getMultiLayoutManagementService(): MultiLayoutManagementService {
    return this.multiLayoutManagementService;
  }

  /**
   * ✅ NEW: Getter method để child components có thể access TabStateManagementService instance
   */
  public getTabStateManagementService(): TabStateManagementService {
    return this.tabStateManagementService;
  }

  ngOnInit(): void {
    this.dynamicLayoutConfigStateService.initialize(this.layoutBuilderConfig);


    // ✅ NEW: Subscribe to confirm switch requests
    this.subscriptions.add(
      this.dynamicLayoutConfigStateService.confirmSwitch$.subscribe(async (confirmData) => {
        if (confirmData) {
          const shouldSwitch = await this.showUnsavedChangesConfirmation(confirmData);

          if (shouldSwitch) {
            const success = this.dynamicLayoutConfigStateService.forceSwitchLayout(confirmData.toLayout._id || '');
            if (!success) {
              this.flashMessageService.error(
                this.translateService.instant(this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.DEBUG_MESSAGES.LAYOUT_SWITCH_FAILED'))
              );
            }
          }
        }
      })
    );
    this.subscriptions.add(
      this.dynamicLayoutConfigStateService.currentLayout$.subscribe(async (layout) => {
        if (layout) {
          this.hasQuickCreate.set(layout.enableQuickCreate || false);
          this.hasDetailViewConfig.set(layout.detailViewConfig ? true : false);
        }
      })
    );
  

    this.dynamicLayoutBuilderStateService.initialize();
    this.multiLayoutManagementService.initializeMultiLayoutSystem(this.layoutBuilderConfig.layouts);
  }

  ngAfterViewInit(): void {
    // setTimeout(() => {
    //   if(this.content.nativeElement) {
    //     const maxHeight = getElementMaxHeightToFit100vh(this.content.nativeElement);
    //     this.content.nativeElement.style.setProperty('height', `${maxHeight}px`);
    //   }
    // });
  }

  ngOnDestroy(): void {
    this.cleanupAllTabComponents();

    // ✅ ENHANCED: Cleanup tất cả services để tránh memory leaks
    this.dynamicLayoutConfigStateService.destroy();
    this.dynamicLayoutBuilderStateService.cleanup();
    this.multiLayoutManagementService.destroy();
    this.tabStateManagementService.destroy();

    this.subscriptions.unsubscribe();
  }

  /**
   * Clean up all tab components to prevent SortableJS errors
   */
  private cleanupAllTabComponents(): void {
    try {
      if (this.createTab && typeof this.createTab.ngOnDestroy === 'function') {
        this.createTab.ngOnDestroy();
      }
    } catch (error) {
      this.flashMessageService.error(
        this.translateService.instant(this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.CONSOLE_MESSAGES.ERROR_CLEANUP_CREATE_TAB'))
      );
    }

    try {
      if (this.quickCreateTab && typeof this.quickCreateTab.ngOnDestroy === 'function') {
        this.quickCreateTab.ngOnDestroy();
      }
    } catch (error) {
      this.flashMessageService.error(
        this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.CONSOLE_MESSAGES.ERROR_CLEANUP_QUICK_CREATE_TAB')
      );
    }

    try {
      if (this.detailViewTab && typeof this.detailViewTab.ngOnDestroy === 'function') {
        this.detailViewTab.ngOnDestroy();
      }
    } catch (error) {
      this.flashMessageService.error(
        this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.CONSOLE_MESSAGES.ERROR_CLEANUP_DETAIL_VIEW_TAB')
      );
    }
  }






 
  onSaveLayout(): void {
    this.saveCurrentLayout();
  }

  /**
   * Toggle preview mode - Mở DynamicLayoutRendererModal để xem trước layout
   * Sử dụng DynamicLayoutRendererModalService.openViewMode() thay vì logic cũ
   */
  async onTogglePreview(): Promise<void> {
    try {
      // Lấy layout config hiện tại từ DynamicLayoutConfigStateService
      const currentLayout = this.dynamicLayoutConfigStateService.currentLayout();

      // Kiểm tra nếu currentLayout không null
      if (!currentLayout) {
        this.flashMessageService.error(
          this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.ERRORS.NO_CURRENT_LAYOUT')
        );
        return;
      }

      // Gọi DynamicLayoutRendererModalService.openViewMode()
      const result = await this.dynamicLayoutRendererModalService.openViewMode(
        {
          // Layout config từ currentLayout (DynamicLayoutConfigDto properties)
          _id: currentLayout._id || '',
          sections: currentLayout.sections,
          quickCreateConfig: currentLayout.quickCreateConfig,
          detailViewConfig: currentLayout.detailViewConfig,
          fieldDefaultSettings: currentLayout.fieldDefaultSettings,
          createdAt: currentLayout.createdAt,
          updatedAt: currentLayout.updatedAt,

          // DynamicLayoutRendererConfig properties bổ sung
          currentPermissionProfileId: 'admin', // Mặc định sử dụng admin permission
          showPermissionProfiles: true, // Hiển thị permission selector
          defaultView: 'view', // Mặc định hiển thị view mode
          showAllTab: false // Chỉ hiển thị view tab
        },
        {
          // Title: "Xem Trước Layout"
          title: 'DYNAMIC_LAYOUT_BUILDER.PREVIEW.MODAL_TITLE',
          // Options: { width: '90vw', height: '80vh' }
          width: '90vw',
          height: '80vh'
        }
      );

      // Xử lý kết quả trả về từ modal (nếu cần)
      if (result) {
        console.log('Preview modal result:', result);
        // Có thể thêm logic xử lý kết quả ở đây nếu cần
      }

    } catch (error) {
      // Error handling cho trường hợp modal open thất bại
      console.error('Error opening preview modal:', error);
      this.flashMessageService.error(
        this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.ERRORS.PREVIEW_MODAL_FAILED')
      );
    }
  }

  /**
   * Cancel current changes and reset to last saved state
   */
  onCancel(): void {
    this.dynamicLayoutConfigStateService.setUnsavedChanges(false);
  }



  /**
   * Handle manual tab switching (for compact layout)
   */
  onTabClick(tabIndex: number): void {
    const previousIndex = this.selectedTabIndex();

    if (previousIndex === tabIndex) {
      return; // Same tab, no change needed
    }


    // Cleanup previous tab
    this.cleanupTabByIndex(previousIndex);

    // Update selected tab index
    this.selectedTabIndex.set(tabIndex);
  }

  /**
   * Clean up specific tab by index to prevent SortableJS errors
   */
  private cleanupTabByIndex(tabIndex: number): void {
    try {
      if(tabIndex === 0) {
        if (this.createTab && typeof this.createTab.cleanupSortableInstances === 'function') {
          this.createTab.cleanupSortableInstances();
        }
      } else if(tabIndex === 3) {
        if (this.detailViewTab && typeof this.detailViewTab.cleanupSortableInstances === 'function') {
          this.detailViewTab.cleanupSortableInstances();
        }
      } else if(tabIndex === 2) {
        if(this.hasQuickCreate()) {
          if (this.quickCreateTab && typeof this.quickCreateTab.cleanupSortableInstances === 'function') {
            this.quickCreateTab.cleanupSortableInstances();
          }
        } else {
          if (this.detailViewTab && typeof this.detailViewTab.cleanupSortableInstances === 'function') {
            this.detailViewTab.cleanupSortableInstances();
          }
        }
      }
    } catch (error) {
      this.flashMessageService.error(
        this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.MESSAGES.ERROR_CLEANING_UP_TAB', { index: tabIndex })
      );
    }
  }

  // ==================== UNSAVED CHANGES CONFIRMATION ====================

  /**
   * Hiển thị modal confirm khi user switch layout mà có unsaved changes
   * @param confirmData - Data về layout switch
   * @returns Promise<boolean> - true nếu user confirm switch, false nếu cancel
   */
  public async showUnsavedChangesConfirmation(confirmData: LayoutSwitchConfirmData): Promise<boolean> {
    console.log('🔄', this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.DEBUG_MESSAGES.SHOWING_UNSAVED_CHANGES'), confirmData);

    try {
      // Sử dụng UnsavedChangesConfirmModalService thay vì MatDialog trực tiếp
      const result = await this.unsavedChangesConfirmModalService.confirm(confirmData);

      if (!result || result.action === 'cancel') {
        console.log('❌', this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.DEBUG_MESSAGES.USER_CANCELLED_SWITCH'));
        return false;
      }

      if (result.action === 'save') {
        console.log('💾', this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.DEBUG_MESSAGES.USER_CHOSE_SAVE_SWITCH'));

        // Trigger save current layout
        await this.saveCurrentLayout();

        // Reset unsaved changes flag
        this.dynamicLayoutConfigStateService.setUnsavedChanges(false);

        return true;
      }

      if (result.action === 'discard') {
        console.log('🗑️', this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.DEBUG_MESSAGES.USER_CHOSE_DISCARD_SWITCH'));

        // Reset unsaved changes flag
        this.dynamicLayoutConfigStateService.setUnsavedChanges(false);

        return true;
      }

      return false;
    } catch (error) {
      console.error('❌', this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.DEBUG_MESSAGES.ERROR_UNSAVED_CHANGES_CONFIRMATION'), error);
      return false;
    }
  }

  /**
   * Save current layout
   * @returns Promise<boolean> - true nếu save thành công
   */
  private async saveCurrentLayout(): Promise<boolean> {
    try {
      console.log('💾', this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.DEBUG_MESSAGES.SAVING_CURRENT_LAYOUT'));

      // Get current sections from state
      const currentState = this.dynamicLayoutConfigStateService.getCurrentState();
      if (!currentState) {
        console.error('❌', this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.DEBUG_MESSAGES.NO_CURRENT_STATE_TO_SAVE'));
        return false;
      }

      // ✅ REFACTORED: Simplified save logic without CoreLayoutBuilderService
      // In real implementation, this would call an API service
      const currentSections = currentState.currentLayout.sections || [];
      console.log('✅', this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.DEBUG_MESSAGES.LAYOUT_SAVED_SUCCESS', { count: currentSections.length }));

      return true;
    } catch (error) {
      console.error('❌', this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.DEBUG_MESSAGES.ERROR_SAVING_LAYOUT'), error);
      return false;
    }
  }
}
