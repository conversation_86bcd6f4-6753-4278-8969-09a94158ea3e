<!-- Quick Create Tab Container -->
<div class="quick-create-tab-container">

  <!-- Left Panel - Reuse Create Tab Field Styling -->
  <div class="fields-sidebar">
    <div class="relative h-100 p-3">

      <!-- Header -->
      <div class="sidebar-header mb-3">
        <h5 class="mb-1">{{ 'DYNAMIC_LAYOUT_BUILDER.QUICK_CREATE.SAVED_FIELDS' | translate }}</h5>
        <p class="text-muted small mb-0">
          {{ 'DYNAMIC_LAYOUT_BUILDER.QUICK_CREATE.SAVED_FIELDS_DESCRIPTION' | translate }}
        </p>
      </div>

      <!-- Saved Sections and Fields -->
      <div class="saved-sections" #availableFieldsPanel>
        @for (section of configStateService.quickCreateFieldSections(); track section._id) {

        <div class="section-group mb-3">
          <!-- Section Header -->
          <div class="section-header d-flex align-items-center justify-content-between p-2 bg-light rounded">
            <div class="d-flex align-items-center">
              <mat-icon class="text-primary me-2">folder</mat-icon>
              <span class="fw-semibold">{{ section.title }}</span>
            </div>
            <div class="badge bg-secondary">{{ section.fields.length }} {{ 'DYNAMIC_LAYOUT_BUILDER.COMMON.FIELDS' |
              translate }}</div>
          </div>

          <!-- Section Fields - CDK Drag & Drop Source -->
          <div class="section-fields saved-fields-list mt-2" cdkDropList [id]="'section-' + section._id"
            [cdkDropListData]="section.fields" [cdkDropListConnectedTo]="['quick-create-drop-zone']"
            cdkDropListSortingDisabled="true" [attr.data-section-id]="section._id">

            @for (field of section.fields; track field._id!) {
            <!-- CDK Draggable Field Item -->
            <div class="field-type-item" [class.disabled]="fieldUsageMap()[field._id!]" cdkDrag [cdkDragData]="field"
              [cdkDragDisabled]="fieldUsageMap()[field._id!]" (cdkDragStarted)="onDragStart($event)"
              (cdkDragEnded)="onDragEnd($event)">

              <!-- Field icon with Create tab styling -->
              <mat-icon class="field-icon" [style.color]="getFieldIconColor(field.type)">
                {{ getFieldIcon(field.type) }}
              </mat-icon>

              <!-- Field info with Create tab styling -->
              <div class="field-info">
                <span class="field-label">{{ field.label }} </span>
              </div>

              <!-- Used indicator với template-specific signal -->
              @if (fieldUsageMap()[field._id!]) {
              <mat-icon class="used-indicator" title="Already used">check_circle</mat-icon>
              }

              <!-- CDK Drag Preview - Fixed: Remove custom class, let CDK handle styling -->
              <div *cdkDragPreview>
                <mat-icon class="field-icon" [style.color]="getFieldIconColor(field.type)">
                  {{ getFieldIcon(field.type) }}
                </mat-icon>
                <span class="field-label">{{ field.label }}</span>
              </div>
            </div>
            } @if (section.fields.length === 0) {
            <div class="text-center text-muted py-3">
              <mat-icon class="mb-2">inbox</mat-icon>
              <div class="small">{{ 'DYNAMIC_LAYOUT_BUILDER.QUICK_CREATE.NO_FIELDS' | translate }}</div>
            </div>
            }
          </div>
        </div>
        } @if (configStateService.quickCreateFieldSections().length === 0) {
        <div class="text-center text-muted py-4">
          <mat-icon class="mb-2" style="font-size: 48px; height: 48px; width: 48px;">folder_open</mat-icon>
          <h6>{{ 'DYNAMIC_LAYOUT_BUILDER.QUICK_CREATE.NO_SECTIONS' | translate }}</h6>
          <p class="small">{{ 'DYNAMIC_LAYOUT_BUILDER.QUICK_CREATE.NO_SECTIONS_DESCRIPTION' | translate }}</p>
        </div>
        }

      </div>

      <!-- Info Panel -->
      <div class="info-panel mt-4 p-3 bg-light rounded">
        <div class="d-flex align-items-center mb-2">
          <mat-icon class="text-info me-2">info</mat-icon>
          <h6 class="mb-0">{{ 'DYNAMIC_LAYOUT_BUILDER.QUICK_CREATE.INFO_TITLE' | translate }}</h6>
        </div>
        <p class="small text-muted mb-0">
          {{ 'DYNAMIC_LAYOUT_BUILDER.QUICK_CREATE.INFO_DESCRIPTION' | translate }}
        </p>
      </div>

    </div>
  </div>

  <!-- Right Panel - Single Section Layout -->
  <div class="layout-main-content">
    <!-- Quick Create Section -->
    <div class="sections-container p-3">

      @if (hasQuickCreateSection()) {
      <!-- Single-Line Field List with CDK Drag & Drop -->
      <div class="quick-create-field-list">
        <!-- Fields List Container - CDK Drop Zone -->
        <div class="fields-list-container" #fieldsListContainer cdkDropList id="quick-create-drop-zone"
          [cdkDropListData]="configStateService.quickCreateSection()!.fields"
          [cdkDropListConnectedTo]="connectedDropLists()" (cdkDropListDropped)="onFieldDrop($event)">

          @if (hasQuickCreateSection() && configStateService.quickCreateSection()!.fields.length > 0) {
          <!-- Field Items - CDK Draggable -->
          @for (field of configStateService.quickCreateSection()!.fields; track field._id || field.label + '-' +
          field.type + '-' + i; let i = $index) {
          <div class="field-list-item" cdkDrag [cdkDragData]="field" (cdkDragStarted)="onDragStart($event)"
            (cdkDragEnded)="onDragEnd($event)">

            <!-- Drag Handle -->
            <div class="drag-handle" cdkDragHandle>
              <mat-icon class="drag-icon">drag_indicator</mat-icon>
            </div>

            <!-- Field Icon -->
             <div
              [matTooltip]="field.label"
             >
              <mat-icon class="field-icon" [style.color]="getFieldIconColor(field.type)">
                {{ getFieldIcon(field.type) }}
              </mat-icon>
            </div>

            <!-- Field Info -->
            <div class="field-info flex-grow-1">
              <span class="field-label">{{ field.label }}</span>
            </div>

            <!-- Actions -->
            <div class="field-actions">
              <button type="button" class="btn btn-sm btn-outline-danger" (click)="onFieldDelete(field)"
                [title]="'DYNAMIC_LAYOUT_BUILDER.TOOLTIPS.REMOVE_FIELD' | translate">
                <mat-icon>close</mat-icon>
              </button>
            </div>

            <!-- CDK Drag Preview - Fixed: Remove custom class, let CDK handle styling -->
            <div *cdkDragPreview>
              <mat-icon class="field-icon" [style.color]="getFieldIconColor(field.type)">
                {{ getFieldIcon(field.type) }}
              </mat-icon>
              <span class="field-label">{{ field.label }}</span>
            </div>
          </div>
          } } @else {
          <!-- Empty Drop Zone Placeholder -->
          <div class="empty-drop-zone-placeholder">
            <div class="drop-zone-content">
              <mat-icon class="drop-zone-icon">add_circle_outline</mat-icon>
              <h6 class="drop-zone-title">{{ 'DYNAMIC_LAYOUT_BUILDER.QUICK_CREATE.DROP_ZONE_TITLE' | translate }}</h6>
              <p class="drop-zone-description">{{ 'DYNAMIC_LAYOUT_BUILDER.QUICK_CREATE.DROP_ZONE_DESCRIPTION' |
                translate }}</p>
            </div>
          </div>
          }
        </div>
      </div>
      } @else {
      <!-- Empty State -->
      <div class="empty-state text-center py-5">
        <mat-icon class="empty-icon text-muted mb-3" style="font-size: 48px; height: 48px; width: 48px;">
          dashboard
        </mat-icon>
        <h5 class="text-muted">
          {{ 'DYNAMIC_LAYOUT_BUILDER.QUICK_CREATE.EMPTY_STATE_TITLE' | translate }}
        </h5>
        <p class="text-muted">
          {{ 'DYNAMIC_LAYOUT_BUILDER.QUICK_CREATE.EMPTY_STATE_DESCRIPTION' | translate }}
        </p>
      </div>
      }




    </div>
  </div>

</div>